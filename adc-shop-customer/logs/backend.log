Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-03T19:37:13+07:00"}
{"level":"info","msg":"Using individual DB config for database connection","time":"2025-06-03T19:37:13+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-03T19:37:13+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (2 handlers)
[GIN-debug] GET    /api/v1/shops             --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/search      --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/popular     --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/nearby      --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/:id         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/:id/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (2 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (2 handlers)
[GIN-debug] GET    /api/v1/menu/items/:itemId --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (2 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (2 handlers)
{"level":"info","msg":"Customer API starting on port 8081","time":"2025-06-03T19:37:13+07:00"}
{"level":"info","msg":"GetShops handler called","time":"2025-06-03T19:37:21+07:00"}
{"level":"info","msg":"Returning success response","time":"2025-06-03T19:37:21+07:00"}
{"level":"info","msg":"GetShops handler called","time":"2025-06-03T19:38:46+07:00"}
{"level":"info","msg":"Returning success response","time":"2025-06-03T19:38:46+07:00"}
{"level":"info","msg":"GetShops handler called","time":"2025-06-03T19:39:44+07:00"}
{"level":"info","msg":"Returning success response","time":"2025-06-03T19:39:44+07:00"}
{"level":"info","msg":"GetShops handler called","time":"2025-06-03T19:39:47+07:00"}
{"level":"info","msg":"Returning success response","time":"2025-06-03T19:39:47+07:00"}
{"level":"info","msg":"GetShops handler called","time":"2025-06-03T19:39:47+07:00"}
{"level":"info","msg":"Returning success response","time":"2025-06-03T19:39:47+07:00"}
{"level":"info","msg":"GetShops handler called","time":"2025-06-03T19:45:53+07:00"}
{"level":"info","msg":"Returning success response","time":"2025-06-03T19:45:53+07:00"}
{"level":"info","msg":"GetShops handler called","time":"2025-06-03T19:54:12+07:00"}
{"level":"info","msg":"Returning success response","time":"2025-06-03T19:54:12+07:00"}
{"level":"info","msg":"GetShops handler called","time":"2025-06-03T20:01:35+07:00"}
{"level":"info","msg":"Returning success response","time":"2025-06-03T20:01:35+07:00"}
{"level":"info","msg":"Shutting down server...","time":"2025-06-03T20:12:00+07:00"}
{"level":"info","msg":"Customer API exited","time":"2025-06-03T20:12:00+07:00"}
make[1]: *** [run] Terminated: 15
